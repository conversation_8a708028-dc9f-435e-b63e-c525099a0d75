import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { AppImages } from "../utils/AppImages";
import { TextInput } from "react-native-gesture-handler";
import PersonalDetailsPopup from "../components/sections/checkout/PersonalDetailsPopup";

import EmptyCart from "../components/Empty/EmptyCart";
import { GetCheckoutApi, getSuggestions } from "../api/Checkout";
import SuggestedItemSlider from "../components/sections/checkout/SuggestedItemSlider";
import { useDispatch, useSelector } from "react-redux";
import { clearSelectedCoupon } from "../redux/slices/couponSlice";

const Card = ({ item, vendor }) => {
  const [quantity, setQuantity] = useState(item.quantity);

  const handleIncrease = () => {
    if (quantity < item.quantity) {
      setQuantity(quantity + 1);
    }
  };

  const handleDecrease = () => quantity > 1 && setQuantity(quantity - 1);

  return (
    <View style={styles.card}>
      <Image source={{ uri: item.image_url }} style={styles.image} />
      <View style={styles.infoContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.title}>
            {item.description.includes("Grocery") ? "Paragon Restaurant" : "Lulu HyperMarket"}
          </Text>
          <Text style={styles.subtitle}>Surprise Bag</Text>
          <Text style={styles.price}>₹{item.actual_price}</Text>
        </View>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            onPress={handleDecrease}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>-</Text>
          </TouchableOpacity>
          <Text style={styles.quantity}>
            {quantity.toString().padStart(2, "0")}
          </Text>
          <TouchableOpacity
            onPress={handleIncrease}
            style={styles.IncreaseQuantityButton}
          >
            <Text style={styles.IncreaseQuantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity style={styles.removeButton}>
        <Text style={styles.removeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );
};

const Cart = ({ navigation, route }) => {
  const vendor = useSelector((state) => state.cart.selectedVendor);
  const selectedCoupon = useSelector((state) => state.coupon.selectedCoupon);
  const dispatch = useDispatch();
  const [showEmptyCart, setShowEmptyCart] = useState(false);
  const [checkoutItems, setCheckoutItems] = useState([]);
  const [paymentBreakdown, setPaymentBreakdown] = useState(null);
  const [suggestions, setSuggestions] = useState([]);

  const fetchSuggetions = async (itemId, vendor_id) => {
    try {
      console.log("inside suggestion API..");
      console.log("ID_Item", itemId);
      console.log("Vendor_id", vendor_id);

      const response = await getSuggestions(vendor_id, itemId);

      setSuggestions(response);
    } catch (error) {
      console.error("Error fetching suggetions:", error);
    }
  };

  const GetCheckoutData = async () => {
    try {
      const response = await GetCheckoutApi();
      console.log("Get Checkout API response", response);

      const checkout = response[0];

      if (!checkout?.checkout_items?.length) {
        setShowEmptyCart(true);
        return;
      }

      const items = checkout.checkout_items.map((item) => ({
        id: item.id,
        image_url: item.image_url,
        description: item.description,
        item_type: item.item_type,
        actual_price: item.actual_price,
        price: item.price,
        quantity: item.quantity,
        vendor_id: checkout.vendor_id,
      }));

      setCheckoutItems(items);
      setPaymentBreakdown(checkout.payment_breakdown);
    } catch (error) {
      console.error("Error fetching checkout data:", error);
    }
  };

  useEffect(() => {
    GetCheckoutData();

    return () => {
      dispatch(clearSelectedCoupon());
    };
  }, []);

  useEffect(() => {
    if (checkoutItems.length > 0) {
      fetchSuggetions(checkoutItems[0].id, checkoutItems[0].vendor_id);
    }
  }, [checkoutItems]);

  const [firstModalVisible, setFirstModalVisible] = useState(false);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingtext}>Cart</Text>
        </View>
      </View>
      {showEmptyCart ? (
        <EmptyCart />
      ) : (
        <ScrollView>
          <View style={styles.flatListConatiner}>
            <FlatList
              data={checkoutItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => <Card item={item} vendor={vendor} />}
            />
          </View>

          <View style={styles.secondSection}>
            <View style={styles.PromoCodeConatiner}>
              <View style={styles.container1}>
                <TextInput
                  value={selectedCoupon?.code || ""}
                  placeholder={selectedCoupon?.code ? "" : "Promo code"}
                  editable={true}
                  style={styles.textInput}
                  placeholderTextColor="#B0B0B0"
                />
                <View style={styles.promoInnerContainer}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("Coupon")}
                  >
                    <Image source={AppImages.PROMO} style={styles.promoImage} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.copyButton}>
                    <Text style={styles.copyText}>Apply</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View>
              <Text
                style={{
                  fontFamily: "Poppins_500Medium",
                  fontSize: 16,
                  marginTop: 14,
                }}
              >
                Complete your purchase with
              </Text>
              <SuggestedItemSlider data={suggestions} />
            </View>

            {/* Allergens & Dislikes Section */}
            <TouchableOpacity
              style={styles.allergensContainer}
              onPress={() => navigation.navigate("Allergens")}
            >
              <View style={styles.allergensContent}>
                <View style={styles.allergensIcon}>
                  <Text style={styles.allergensIconText}>🥜</Text>
                </View>
                <Text style={styles.allergensText}>Allergens & Dislikes</Text>
              </View>
              <TouchableOpacity style={styles.selectButton}>
                <Text style={styles.selectButtonText}>Select</Text>
              </TouchableOpacity>
            </TouchableOpacity>
            <View style={styles.billCard}>
              <Text style={styles.header}>Total Bill</Text>

              <View style={styles.row}>
                <Text style={styles.label}>
                  Quantity ({checkoutItems.reduce((acc, item) => acc + item.quantity, 0)} items)
                </Text>
                <Text style={styles.boldText}>
                  ₹{paymentBreakdown?.subtotal?.toFixed(0) || "12"}
                </Text>
              </View>

              {paymentBreakdown?.coupon_discount > 0 && (
                <View style={styles.row}>
                  <Text style={styles.label}>Voucher</Text>
                  <Text style={styles.purpleText}>
                    -₹{paymentBreakdown.coupon_discount.toFixed(0)}
                  </Text>
                </View>
              )}

              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>
                  Total: ₹{paymentBreakdown?.final_amount?.toFixed(0) || "37,50"}
                </Text>
                <Text style={styles.itemsCount}>
                  {checkoutItems.reduce((acc, item) => acc + item.quantity, 0)} items
                </Text>
              </View>
            </View>

            <TouchableOpacity style={styles.payButton}>
              <Text style={styles.payText}>Pay</Text>
            </TouchableOpacity>
            <PersonalDetailsPopup
              firstModalVisible={firstModalVisible}
              setFirstModalVisible={setFirstModalVisible}
            />
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  conatinerRoww: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  flatListConatiner: {
    backgroundColor: "#FFF",
    padding: 20,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  secondSection: {
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: "#FFFFFF",
  },
  PromoCodeConatiner: {
    backgroundColor: "#FBFBFBBA",
    padding: 16,
    paddingLeft: 20,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingRight: 20,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  promoInnerConatiner: {
    flexDirection: "row",
    gap: 6,
    alignItems: "center",
  },
  totalPriceInnerConatinner: {
    flexDirection: "row",
    gap: 1,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  headingtext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    color: "#5F22D9",
    marginRight: 35,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    marginVertical: 8,
    paddingVertical: 12,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
    resizeMode: "cover",
    overflow: "hidden",
  },
  infoContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 5,
    paddingRight: 20,
    marginLeft: 10,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#000000",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#8C8A9D",
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 10,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
  IncreaseQuantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderColor: "#5F22D9",
    backgroundColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  quantityButtonText: {
    fontSize: 20,
    color: "#5F22D9",
    lineHeight: 20,
    textAlign: "center",
  },

  IncreaseQuantityButtonText: {
    fontSize: 20,
    color: "#FFF",
    lineHeight: 20,
    textAlign: "center",
  },

  quantity: {
    fontFamily: "Poppins_600SemiBold",
    marginHorizontal: 12,
    fontSize: 16,
    color: "#000",
    minWidth: 24,
    textAlign: "center",
  },
  removeButton: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center",
  },
  removeButtonText: {
    fontSize: 16,
    color: "#5F22D9",
    fontWeight: "bold",
    lineHeight: 16,
  },
  container1: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 25,
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "100%",
    alignSelf: "center",
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#000",
    marginRight: 10,
  },
  promoInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    maxWidth: "40%",
    flexShrink: 1,
  },
  promoImage: {
    width: 25,
    height: 25,
    resizeMode: "contain",
  },
  copyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  copyText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  billCard: {
    backgroundColor: "#FBFBFBBA",
    borderRadius: 14,
    padding: 16,
    marginTop: 10,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 10,
  },
  header: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#171725",
    marginBottom: 10,
  },
  rowContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  label: {
    fontSize: 16,
  },
  boldText: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
  },
  purpleText: {
    fontSize: 16,
    color: "#5F22D9",
  },
  total: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  strikeThrough: {
    fontSize: 14,
    color: "#A0A0A0",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  finalAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#5F22D9",
  },
  paymentContainer: {
    marginTop: 20,
  },
  paymentMethod: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  cardDetails: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  cardNumber: {
    fontSize: 16,
    color: "#666",
    flex: 1,
    marginLeft: 10,
  },
  changeButton: {
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 17,
    borderColor: "#EDEFFF",
    borderWidth: 2,
  },
  changeText: {
    color: "#5F22D9",
    fontWeight: "bold",
    fontSize: 16,
  },
  totalContainer: {
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  totalTextPrice: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  itemsText: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#898EBC",
  },
  payButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 18,
    paddingHorizontal: 70,
    borderRadius: 18,
    alignItems: "center",
  },
  payText: {
    color: "#FFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  allergensContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  allergensContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  allergensIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  allergensIconText: {
    fontSize: 20,
  },
  allergensText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#8E8E8E",
  },
  selectButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  selectButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  totalLabel: {
    fontSize: 18,
    fontFamily: "Poppins_600SemiBold",
    color: "#171725",
  },
  itemsCount: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#898EBC",
  },
});

export default Cart;
