import { createSlice } from "@reduxjs/toolkit";

const cartSlice = createSlice({
  name: "cart",
  initialState: {
    selectedItem: null,
    selectedVendor: null,
    cartItems: [],
    checkoutData: null,
    paymentBreakdown: null,
    isLoading: false,
  },
  reducers: {
    setSelectedItem: (state, action) => {
      state.selectedItem = action.payload;
    },
    setSelectedVendor: (state, action) => {
      state.selectedVendor = action.payload;
    },
    setCartItems: (state, action) => {
      state.cartItems = action.payload;
    },
    setCheckoutData: (state, action) => {
      state.checkoutData = action.payload;
      if (action.payload?.checkout_items) {
        state.cartItems = action.payload.checkout_items;
      }
      if (action.payload?.payment_breakdown) {
        state.paymentBreakdown = action.payload.payment_breakdown;
      }
    },
    setPaymentBreakdown: (state, action) => {
      state.paymentBreakdown = action.payload;
    },
    updateItemQuantity: (state, action) => {
      const { itemId, quantity } = action.payload;
      const item = state.cartItems.find(item => item.id === itemId);
      if (item) {
        item.quantity = quantity;
      }
    },
    removeCartItem: (state, action) => {
      state.cartItems = state.cartItems.filter(item => item.id !== action.payload);
    },
    clearCart: (state) => {
      state.cartItems = [];
      state.checkoutData = null;
      state.paymentBreakdown = null;
    },
    setCartLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    addSuggestedItem: (state, action) => {
      const newItem = action.payload;
      const existingItem = state.cartItems.find(item => item.id === newItem.id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        state.cartItems.push({ ...newItem, quantity: 1 });
      }
    },
  },
});

export const {
  setSelectedItem,
  setSelectedVendor,
  setCartItems,
  setCheckoutData,
  setPaymentBreakdown,
  updateItemQuantity,
  removeCartItem,
  clearCart,
  setCartLoading,
  addSuggestedItem
} = cartSlice.actions;
export default cartSlice.reducer;
