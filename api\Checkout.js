import axiosClient from "../AxiosClient";

export const CheckoutApi = async (data) => {
  console.log("🔄 CheckoutApi called with data:", JSON.stringify(data, null, 2));
  console.log("🌐 Making request to: user/checkout/create");

  try {
    const response = await axiosClient.post(`user/checkout/create`, data);
    console.log("✅ CheckoutApi response:", JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.log("❌ CheckoutApi error:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", JSON.stringify(error.response.data, null, 2));
      console.log("📝 Error response headers:", error.response.headers);
    }
    throw error; // Re-throw to maintain error handling in calling code
  }
};

export const GetCheckoutApi = async () => {
  const response = await axiosClient.get(`user/checkout/get`);
  return response.data;
};

export const getSuggestions = async (vendor_id, item_id) => {
  console.log("ids in parsms");
  console.log(vendor_id, item_id);
  const response = await axiosClient.get(
    `user/discover/items/suggestions/get?vendor_id=${vendor_id}&item_id=${item_id}`
  );
  console.log("Inside get suggestions API...");
  console.log(response.data);
  return response.data;
};

export const getCoupons = async () => {
  const response = await axiosClient.get(`user/checkout/coupon-code/get`);
  return response.data;
};
