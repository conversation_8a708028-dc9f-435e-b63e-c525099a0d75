import axiosClient from "../AxiosClient";

export const CheckoutApi = async (data) => {
  console.log("🔄 CheckoutApi called with data:", JSON.stringify(data, null, 2));
  console.log("🌐 Making request to: user/checkout/create");

  try {
    const response = await axiosClient.post(`user/checkout/create`, data);
    console.log("✅ CheckoutApi response:", JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.log("❌ CheckoutApi error:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", JSON.stringify(error.response.data, null, 2));
      console.log("📝 Error response headers:", error.response.headers);
    }
    throw error; // Re-throw to maintain error handling in calling code
  }
};

export const GetCheckoutApi = async () => {
  const response = await axiosClient.get(`user/checkout/get`);
  return response.data;
};

export const getSuggestions = async (vendor_id, item_ids) => {
  console.log("🔍 Getting suggestions for vendor:", vendor_id, "items:", item_ids);

  try {
    // Handle both single item_id and array of item_ids
    const itemIdsParam = Array.isArray(item_ids) ? item_ids.join(',') : item_ids;

    const response = await axiosClient.get(
      `user/discover/items/suggestions/get?vendor_id=${vendor_id}&item_id=${itemIdsParam}`
    );

    console.log("✅ Suggestions API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching suggestions:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};

export const getCoupons = async () => {
  const response = await axiosClient.get(`user/checkout/coupon-code/get`);
  return response.data;
};

export const updateCheckoutItemQuantity = async (checkoutItemId, quantity) => {
  console.log("🔄 Updating checkout item quantity:", { checkoutItemId, quantity });
  try {
    const response = await axiosClient.put(`user/checkout/item/update`, {
      checkout_item_id: checkoutItemId,
      quantity: quantity
    });
    console.log("✅ Update quantity response:", response.data);
    return response.data;
  } catch (error) {
    console.log("❌ Update quantity error:", error);
    throw error;
  }
};

export const removeCheckoutItem = async (checkoutItemId) => {
  console.log("🗑️ Removing checkout item:", checkoutItemId);
  try {
    const response = await axiosClient.delete(`user/checkout/item/remove`, {
      data: { checkout_item_id: checkoutItemId }
    });
    console.log("✅ Remove item response:", response.data);
    return response.data;
  } catch (error) {
    console.log("❌ Remove item error:", error);
    throw error;
  }
};

export const updateCheckout = async (checkoutId, updateData) => {
  console.log("🔄 Updating checkout:", checkoutId, "with data:", updateData);
  try {
    const response = await axiosClient.put(`user/checkout/update?checkout_id=${checkoutId}`, updateData);
    console.log("✅ Update checkout response:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Update checkout error:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};
