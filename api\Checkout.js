import axiosClient from "../AxiosClient";

export const CheckoutApi = async (data) => {
  console.log("🔄 CheckoutApi called with data:", JSON.stringify(data, null, 2));
  console.log("🌐 Making request to: user/checkout/create");

  try {
    const response = await axiosClient.post(`user/checkout/create`, data);
    console.log("✅ CheckoutApi response:", JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.log("❌ CheckoutApi error:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", JSON.stringify(error.response.data, null, 2));
      console.log("📝 Error response headers:", error.response.headers);
    }
    throw error; // Re-throw to maintain error handling in calling code
  }
};

export const GetCheckoutApi = async () => {
  const response = await axiosClient.get(`user/checkout/get`);
  return response.data;
};

export const getSuggestions = async (vendor_id, item_ids) => {
  console.log("🔍 Getting suggestions for vendor:", vendor_id, "items:", item_ids);

  try {
    // Handle both single item_id and array of item_ids
    const itemIdsParam = Array.isArray(item_ids) ? item_ids.join(',') : item_ids;

    const response = await axiosClient.get(
      `user/discover/items/suggestions/get?vendor_id=${vendor_id}&item_id=${itemIdsParam}`
    );

    console.log("✅ Suggestions API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching suggestions:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};

export const getCoupons = async () => {
  const response = await axiosClient.get(`user/checkout/coupon-code/get`);
  return response.data;
};

export const updateCheckoutItemQuantity = async (checkoutId, itemId, quantity) => {
  console.log("🔄 Updating checkout item quantity:", { checkoutId, itemId, quantity });
  try {
    // First get current checkout data to preserve other items
    const currentCheckout = await GetCheckoutApi();
    if (!currentCheckout || !currentCheckout.length) {
      throw new Error("No active checkout found");
    }

    const checkout = currentCheckout[0];
    console.log("📋 Current checkout before update:", checkout);

    // Find the item to update and preserve all other items
    let itemFound = false;
    const updatedItems = checkout.checkout_items.map(item => {
      if (item.id === itemId) {
        itemFound = true;
        console.log(`📝 Updating item ${itemId} quantity from ${item.quantity} to ${quantity}`);
        return { item_id: item.id, quantity: quantity };
      }
      return { item_id: item.id, quantity: item.quantity };
    });

    if (!itemFound) {
      throw new Error(`Item with ID ${itemId} not found in checkout`);
    }

    // Use the updateCheckout function with correct format
    const updateData = {
      dislikes: checkout.dislikes || [],
      items: updatedItems
    };

    console.log("📤 Sending update data:", updateData);

    const response = await updateCheckout(checkoutId, updateData);
    console.log("✅ Update quantity response:", response);

    // Validate the response has the expected structure
    if (!response || !response.checkout_items) {
      console.warn("⚠️ Unexpected response structure:", response);
      // Return the current checkout data if response is invalid
      return checkout;
    }

    return response;
  } catch (error) {
    console.error("❌ Update quantity error:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};

export const removeCheckoutItem = async (checkoutId, itemId) => {
  console.log("🗑️ Removing checkout item:", { checkoutId, itemId });
  try {
    // First get current checkout data to preserve other items
    const currentCheckout = await GetCheckoutApi();
    if (!currentCheckout || !currentCheckout.length) {
      throw new Error("No active checkout found");
    }

    const checkout = currentCheckout[0];

    // Remove the specific item from the list
    const updatedItems = checkout.checkout_items
      .filter(item => item.id !== itemId)
      .map(item => ({
        item_id: item.id,
        quantity: item.quantity
      }));

    // Use the updateCheckout function with correct format
    const updateData = {
      dislikes: checkout.dislikes || [],
      items: updatedItems
    };

    const response = await updateCheckout(checkoutId, updateData);
    console.log("✅ Remove item response:", response);
    return response;
  } catch (error) {
    console.log("❌ Remove item error:", error);
    throw error;
  }
};

export const updateCheckout = async (checkoutId, updateData) => {
  console.log("🔄 Updating checkout:", checkoutId, "with data:", updateData);

  if (!checkoutId) {
    throw new Error("Checkout ID is required");
  }

  try {
    // Transform the data to match API format
    const apiData = {
      dislikes: updateData.dislikes || [],
      items: updateData.items || updateData.checkout_items?.map(item => ({
        item_id: item.item_id,
        quantity: item.quantity
      })) || []
    };

    console.log("📝 Transformed API data:", apiData);
    console.log("🌐 Making API call to:", `user/checkout/update?checkout_id=${checkoutId}`);

    const response = await axiosClient.post(`user/checkout/update?checkout_id=${checkoutId}`, apiData);
    console.log("✅ Update checkout response status:", response.status);
    console.log("✅ Update checkout response data:", response.data);

    // Validate response structure
    if (!response.data) {
      console.warn("⚠️ Empty response data");
      throw new Error("Empty response from server");
    }

    if (!response.data.checkout_items) {
      console.warn("⚠️ Response missing checkout_items:", response.data);
    }

    return response.data;
  } catch (error) {
    console.error("❌ Update checkout error:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
      console.error("📋 Error response headers:", error.response.headers);
    } else if (error.request) {
      console.error("📡 No response received:", error.request);
    } else {
      console.error("⚙️ Error setting up request:", error.message);
    }
    throw error;
  }
};
