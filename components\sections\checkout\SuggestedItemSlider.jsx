import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  ActivityIndicator,
  Alert,
} from "react-native";
import { AppImages } from "../../../utils/AppImages";
import { updateCheckout } from "../../../api/Checkout";
import { useSelector, useDispatch } from "react-redux";
import { setCheckoutData } from "../../../redux/slices/cartSlice";

const SCREEN_WIDTH = Dimensions.get("window").width;
const CARD_WIDTH = SCREEN_WIDTH * 0.4;

const SuggestedItemSlider = ({ data, onRefreshCart }) => {
  const [addingItems, setAddingItems] = useState(new Set());
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const dispatch = useDispatch();

  const handleAddSuggestedItem = async (item) => {
    if (!checkoutData?.checkout_id) {
      Alert.alert("Error", "No active checkout found");
      return;
    }

    if (addingItems.has(item.id)) return;

    setAddingItems(prev => new Set([...prev, item.id]));

    try {
      console.log("🛒 Adding suggested item to checkout:", item);

      // Prepare the update data with the new item using correct API format
      const updateData = {
        checkout_items: [
          ...checkoutData.checkout_items.map(existingItem => ({
            item_id: existingItem.id,
            quantity: existingItem.quantity
          })),
          {
            item_id: item.id,
            quantity: 1
          }
        ],
        dislikes: checkoutData.dislikes || []
      };

      console.log("📝 Update checkout payload:", updateData);

      const response = await updateCheckout(checkoutData.checkout_id, updateData);
      console.log("✅ Checkout updated with suggested item:", response);

      // Update Redux state with new checkout data
      dispatch(setCheckoutData(response));

      // Refresh cart if callback provided
      if (onRefreshCart) {
        onRefreshCart();
      }

      Alert.alert("Success", "Item added to cart successfully!");

    } catch (error) {
      console.error("❌ Error adding suggested item:", error);
      Alert.alert("Error", "Failed to add item to cart. Please try again.");
    } finally {
      setAddingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(item.id);
        return newSet;
      });
    }
  };

  const renderSuperMarketCard = ({ item }) => {
    const displayName = item.item_type
      ?.replace(/_/g, " ")
      ?.replace(/\b\w/g, (l) => l.toUpperCase()) || item.description || "Item";

    const isAdding = addingItems.has(item.id);

    return (
      <View style={[styles.cardContainer, { width: CARD_WIDTH }]}>
        <View style={styles.card}>
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: item.image_url }}
              style={styles.image}
              resizeMode="cover"
            />
          </View>

          <View style={styles.textContainer}>
            <Text style={styles.name}>
              {displayName.length > 18
                ? displayName.slice(0, 18) + "..."
                : displayName}
            </Text>
            <Text style={styles.price}>₹{item.price || item.actual_price}</Text>
          </View>

          <View style={styles.addButtonContainer}>
            <Text style={styles.addText}>Add</Text>
            <TouchableOpacity
              style={[styles.IncreaseQuantityButton, isAdding && styles.disabledButton]}
              onPress={() => handleAddSuggestedItem(item)}
              disabled={isAdding}
            >
              {isAdding ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.IncreaseQuantityButtonText}>+</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <FlatList
      data={data}
      renderItem={renderSuperMarketCard}
      keyExtractor={(item) => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      snapToAlignment="start"
      decelerationRate="fast"
      snapToInterval={CARD_WIDTH}
      contentContainerStyle={styles.sliderContainer}
    />
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    marginTop: 10,
  },
  cardContainer: {
    marginHorizontal: 10,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 0.5,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1.5,
    elevation: 0.5,
    marginBottom: 5,
  },
  card: {
    backgroundColor: "#FFFFFF",
    position: "relative",
    borderRadius: 20,
    height: "auto",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  imageWrapper: {
    position: "relative",
  },
  image: {
    width: "100%",
    height: 90,
    borderRadius: 10,
  },
  removeButton: {
    position: "absolute",
    top: -4,
    right: -6,
    backgroundColor: "#5F22D9",
    borderRadius: 15,
    width: 22,
    height: 22,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  removeButtonText: {
    fontSize: 14,
    color: "#FFF",
    fontWeight: "bold",
    lineHeight: 14,
  },
  IncreaseQuantityButton: {
    width: 20,
    height: 20,
    borderRadius: 15,
    borderColor: "#5F22D9",
    backgroundColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  IncreaseQuantityButtonText: {
    fontSize: 16,
    color: "#FFF",
    lineHeight: 16,
    textAlign: "center",
  },
  textContainer: {
    padding: 10,
    paddingTop: 5,
    alignItems: "flex-start",
  },
  name: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#000000",
    textAlign: "center",
    marginBottom: 4,
  },
  price: {
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    textAlign: "center",
    marginBottom: 8,
  },
  addButtonContainer: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
    width: "100%",
    justifyContent: "center",
    marginBottom: 10,
  },
  addText: {
    color: "#666",
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
  },
  disabledButton: {
    opacity: 0.5,
  },
  type: {
    fontFamily: "Poppins_500Medium",
    fontSize: 12,
    color: "#000",
  },
});

export default SuggestedItemSlider;
