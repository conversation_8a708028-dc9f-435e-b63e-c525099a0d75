import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

const CartDebugger = ({ visible = false }) => {
  const cartItems = useSelector((state) => state.cart.cartItems);
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const paymentBreakdown = useSelector((state) => state.cart.paymentBreakdown);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🐛 Cart Debug Info</Text>
      
      <Text style={styles.section}>Cart Items ({cartItems?.length || 0}):</Text>
      {cartItems?.map((item, index) => (
        <Text key={index} style={styles.item}>
          • {item.id}: qty {item.quantity}
        </Text>
      ))}
      
      <Text style={styles.section}>Checkout Data:</Text>
      <Text style={styles.item}>
        ID: {checkoutData?.checkout_id || 'None'}
      </Text>
      <Text style={styles.item}>
        Items: {checkoutData?.checkout_items?.length || 0}
      </Text>
      
      <Text style={styles.section}>Payment:</Text>
      <Text style={styles.item}>
        Total: ₹{paymentBreakdown?.final_amount || 0}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    margin: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  section: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 5,
  },
  item: {
    fontSize: 12,
    marginLeft: 10,
    marginBottom: 2,
  },
});

export default CartDebugger;
